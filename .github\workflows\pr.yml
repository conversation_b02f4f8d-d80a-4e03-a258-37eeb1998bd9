name: Pull Request
on:
  pull_request_target:
    types: [opened]
jobs:
  pull_request_comment:
    name: Notify third-party contributor
    if: github.event.pull_request.head.repo.full_name != github.repository
    runs-on: ubuntu-latest
    steps:
      - name: Add comment to <PERSON> if coming from third-party fork
        uses: mshick/add-pr-comment@v1
        with:
          message: |
            Thank you for opening this PR! We appreciate you!

            For all pull requests coming from third-party forks we will need to
            review the PR before we can process it through our CI pipelines.

            A Forem Team member will review this contribution and get back to
            you as soon as possible!
          repo-token: ${{ secrets.GITHUB_TOKEN }}
          repo-token-user-login: 'github-actions[bot]'

class ImageUsage < ApplicationRecord
  belongs_to :image_upload
  belongs_to :usable, polymorphic: true
  
  validates :usage_type, presence: true
  validates :image_upload_id, uniqueness: { scope: [:usable_type, :usable_id, :usage_type] }
  
  enum usage_type: {
    body_content: 0,
    main_image: 1,
    social_image: 2,
    profile_image: 3,
    organization_image: 4,
    other: 99
  }
  
  scope :for_articles, -> { where(usable_type: 'Article') }
  scope :for_comments, -> { where(usable_type: 'Comment') }
  scope :for_users, -> { where(usable_type: 'User') }
  
  after_create :mark_image_as_used
  after_destroy :check_if_image_still_used
  
  def self.track_usage(image_upload, usable, usage_type = :body_content)
    find_or_create_by(
      image_upload: image_upload,
      usable: usable,
      usage_type: usage_type
    )
  end
  
  def self.remove_usage(image_upload, usable, usage_type = :body_content)
    where(
      image_upload: image_upload,
      usable: usable,
      usage_type: usage_type
    ).destroy_all
  end
  
  def self.sync_article_images(article)
    # Remove existing usages for this article
    where(usable: article).destroy_all
    
    # Find all images referenced in the article
    image_urls = extract_image_urls_from_content(article.body_markdown, article.processed_html)
    
    image_urls.each do |url|
      image_upload = find_image_upload_by_url(url)
      next unless image_upload
      
      track_usage(image_upload, article, :body_content)
    end
    
    # Track main image if present
    if article.main_image.present?
      main_image_upload = find_image_upload_by_url(article.main_image)
      track_usage(main_image_upload, article, :main_image) if main_image_upload
    end
    
    # Track social image if present
    if article.social_image.present?
      social_image_upload = find_image_upload_by_url(article.social_image)
      track_usage(social_image_upload, article, :social_image) if social_image_upload
    end
  end
  
  def self.sync_comment_images(comment)
    # Remove existing usages for this comment
    where(usable: comment).destroy_all
    
    # Find all images referenced in the comment
    image_urls = extract_image_urls_from_content(comment.body_markdown, comment.processed_html)
    
    image_urls.each do |url|
      image_upload = find_image_upload_by_url(url)
      next unless image_upload
      
      track_usage(image_upload, comment, :body_content)
    end
  end
  
  private
  
  def mark_image_as_used
    image_upload.mark_as_used!
  end
  
  def check_if_image_still_used
    # If this was the last usage, mark image as unused
    if image_upload.image_usages.empty?
      image_upload.mark_as_unused!
    end
  end
  
  def self.extract_image_urls_from_content(markdown, html)
    urls = []
    
    # Extract from markdown
    if markdown.present?
      # Markdown image syntax: ![alt](url)
      markdown_urls = markdown.scan(/!\[.*?\]\((.*?)\)/).flatten
      urls.concat(markdown_urls)
      
      # HTML img tags in markdown
      html_urls = markdown.scan(/<img.*?src=["'](.*?)["']/).flatten
      urls.concat(html_urls)
    end
    
    # Extract from processed HTML
    if html.present?
      html_urls = html.scan(/<img.*?src=["'](.*?)["']/).flatten
      urls.concat(html_urls)
    end
    
    # Filter to only include uploaded images (local URLs)
    urls.select { |url| url.start_with?('/uploads/') || url.include?('/uploads/') }
  end
  
  def self.find_image_upload_by_url(url)
    # Extract filename from URL
    filename = File.basename(url)
    ImageUpload.find_by(filename: filename)
  end
end

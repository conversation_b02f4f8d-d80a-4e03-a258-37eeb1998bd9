module Constants
  module Settings
    module Images
      def self.details
        {
          orphaned_image_retention_days: {
            description: "Number of days to retain orphaned uploaded images before cleanup (default: 7)",
            placeholder: 7
          },
          enable_orphaned_image_cleanup: {
            description: "Enable automatic cleanup of orphaned uploaded images",
            placeholder: true
          },
          max_user_storage_mb: {
            description: "Maximum storage per user for uploaded images in MB (0 = unlimited)",
            placeholder: 0
          },
          image_cleanup_batch_size: {
            description: "Number of images to process in each cleanup batch",
            placeholder: 100
          }
        }
      end
    end
  end
end

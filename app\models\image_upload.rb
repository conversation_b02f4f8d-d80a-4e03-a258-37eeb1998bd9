class ImageUpload < ApplicationRecord
  belongs_to :user
  
  validates :filename, presence: true
  validates :file_path, presence: true
  validates :file_size, presence: true, numericality: { greater_than: 0 }
  validates :content_type, presence: true
  
  scope :orphaned, -> { where(used_in_content: false) }
  scope :older_than, ->(date) { where("created_at < ?", date) }
  scope :unused_for_days, ->(days) { orphaned.older_than(days.days.ago) }
  
  # Track where this image is used
  has_many :image_usages, dependent: :destroy
  has_many :articles, through: :image_usages, source: :usable, source_type: 'Article'
  has_many :comments, through: :image_usages, source: :usable, source_type: 'Comment'
  
  after_create :check_usage_after_delay
  
  def mark_as_used!
    update!(used_in_content: true, used_at: Time.current)
  end
  
  def mark_as_unused!
    update!(used_in_content: false, used_at: nil)
  end
  
  def orphaned?
    !used_in_content?
  end
  
  def days_since_upload
    (Time.current - created_at) / 1.day
  end
  
  def eligible_for_cleanup?(retention_days = 7)
    orphaned? && days_since_upload >= retention_days
  end
  
  def file_url
    # Return the public URL for this image
    if file_path.start_with?('/uploads/')
      file_path
    else
      "/uploads/#{file_path}"
    end
  end
  
  def full_file_path
    Rails.root.join("public#{file_url}")
  end
  
  def file_exists?
    File.exist?(full_file_path)
  end
  
  def delete_file!
    if file_exists?
      File.delete(full_file_path)
      Rails.logger.info("Deleted image file: #{full_file_path}")
    else
      Rails.logger.warn("Image file not found: #{full_file_path}")
    end
  end
  
  def self.cleanup_orphaned!(retention_days = 7)
    cleanup_count = 0
    storage_freed = 0
    
    unused_for_days(retention_days).find_each do |image|
      begin
        storage_freed += image.file_size
        image.delete_file!
        image.destroy!
        cleanup_count += 1
      rescue StandardError => e
        Rails.logger.error("Failed to cleanup image #{image.id}: #{e.message}")
      end
    end
    
    Rails.logger.info("Cleaned up #{cleanup_count} orphaned images, freed #{storage_freed} bytes")
    
    { count: cleanup_count, bytes_freed: storage_freed }
  end
  
  def self.total_storage_for_user(user)
    where(user: user).sum(:file_size)
  end
  
  def self.check_user_storage_quota(user, new_file_size)
    max_storage = Settings::Images.max_user_storage_mb.megabytes
    return true if max_storage.zero? # Unlimited
    
    current_storage = total_storage_for_user(user)
    (current_storage + new_file_size) <= max_storage
  end
  
  private
  
  def check_usage_after_delay
    # Check if image is used in content after a delay
    # This allows time for the user to save their article/comment
    Images::CheckUsageWorker.perform_in(5.minutes, id)
  end
end

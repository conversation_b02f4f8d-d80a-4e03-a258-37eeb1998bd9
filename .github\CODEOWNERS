# CODEOWNERS lists which teams are largely responsible for areas of code in the repository
# see https://help.github.com/en/github/creating-cloning-and-archiving-repositories/about-code-owners
# for further details

# Default global ownership
* @forem/core-reviewers

# Dependency ownership
Gemfile      @forem/core-reviewers @forem/platform
Gemfile.lock @forem/core-reviewers @forem/platform
package.json @forem/core-reviewers @forem/platform
yarn.lock    @forem/core-reviewers @forem/platform

# Platform specific ownership
.buildkite/                              @forem/platform @forem/core-reviewers
Containerfile                            @forem/platform @forem/core-reviewers
Dockerfile                               @forem/platform @forem/core-reviewers
app/controllers/async_info_controller.rb @forem/platform @forem/core-reviewers
app/services/search/                     @forem/platform @forem/core-reviewers
app/workers/                             @forem/platform @forem/core-reviewers
config/                                  @forem/platform @forem/core-reviewers
db/                                      @forem/platform @forem/core-reviewers
docker-compose.yml                       @forem/platform @forem/core-reviewers
lib/                                     @forem/platform @forem/core-reviewers
podman-compose.yml                       @forem/platform @forem/core-reviewers
scripts/                                 @forem/platform @forem/core-reviewers
spec/rails_helper.rb                     @forem/platform @forem/core-reviewers
spec/support/                            @forem/platform @forem/core-reviewers

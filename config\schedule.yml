feeds_import:
  description: "Imports feed items as articles (runs hourly on the 20th minute after the hour)"
  cron: "20 * * * *"
  class: "Feeds::ImportArticlesWorker"
log_worker_queue_stats:
  description: "Records Sidekiq stats in Datadog (runs every 10 minutes)"
  cron: "*/10 * * * *"
  class: "Metrics::RecordBackgroundQueueStatsWorker"
record_daily_usage:
  description: "Records daily usage stats (runs daily at 11:00 UTC)"
  cron: "0 11 * * *"
  class: "Metrics::RecordDailyUsageWorker"
record_daily_notifications:
  description: "Records daily notifications stats (runs daily at 11:00 UTC)"
  cron: "0 11 * * *"
  class: "Metrics::RecordDailyNotificationsWorker"
record_data_counts:
  description: "Records the size of the most important tables in the database (runs hourly on the 10th minute after the hour)"
  cron: "10 * * * *"
  class: "Metrics::RecordDataCountsWorker"
check_data_update_script_statuses:
  description: "Records the statuses of data update scripts (runs hourly on the 30th minute after the hour)"
  cron: "30 * * * *"
  class: "Metrics::CheckDataUpdateScriptStatuses"
award_yearly_club_badges:
  description: "Awards 'yearly club' badges to users (runs daily at 00:00 UTC)"
  cron: "0 0 * * *"
  class: "BadgeAchievements::BadgeAwardWorker"
  args:
    - ""
    - award_yearly_club
    - ""
award_beloved_comment_badges:
  description: "Awards 'beloved comment' badges to users (runs every 12 hours on the 5th minute after the hour)"
  cron: "5 */12 * * *"
  class: "BadgeAchievements::BadgeAwardWorker"
  args:
    - ""
    - award_beloved_comment
    - ""
award_four_week_streak_badge:
  description: "Awards 'four week publishing streak' badges to users (runs every 12 hours on the 10th minute after the hour)"
  cron: "10 */12 * * *"
  class: "BadgeAchievements::BadgeAwardWorker"
  args:
    - ""
    - award_four_week_streak
    - ""
award_eight_week_streak_badge:
  description: "Awards 'eight week publishing streak' badges to users (runs every 12 hours on the 15th minute after the hour)"
  cron: "15 */12 * * *"
  class: "BadgeAchievements::BadgeAwardWorker"
  args:
    - ""
    - award_eight_week_streak
    - ""
award_sixteen_week_streak_badge:
  description: "Awards 'sixteen week publishing streak' badges to users (runs every 12 hours on the 20th minute after the hour)"
  cron: "20 */12 * * *"
  class: "BadgeAchievements::BadgeAwardWorker"
  args:
    - ""
    - award_sixteen_week_streak
    - ""
award_weekly_tag_badges:
  description: "Awards 'weekly beloved article for a tag' badges to users (runs at 11:00 UTC on Thursday)"
  cron: "0 11 * * 4"
  class: "BadgeAchievements::BadgeAwardWorker"
  args:
    - ""
    - award_tag
    - ""
award_contributor_badges_from_github:
  description: "Awards 'GitHub contributor' badges to users (runs hourly on the 20th minute after the hour)"
  cron: "20 * * * *"
  class: "BadgeAchievements::BadgeAwardWorker"
  args:
    - ""
    - award_contributor_from_github
    - ""
award_community_wellness_badges:
  description: "Awards 'Community Wellness' badges to users (runs daily at 00:45 UTC)"
  cron: "45 0 * * *"
  class: "BadgeAchievements::BadgeAwardWorker"
  args:
    - ""
    - award_community_wellness
    - ""
award_thumbs_up_badges:
  description: "Awards Thumbs Up Milestone badge to users (runs daily at 01:00 UTC) "
  cron: "0 1 * * *"
  class: "BadgeAchievements::BadgeAwardWorker"
  args:
    - ""
    - award_thumbs_up
    - ""
award_first_post_badges:
  description: "Awards First Post badges to users (runs daily at 02:00 UTC) "
  cron: "0 2 * * *"
  class: "BadgeAchievements::BadgeAwardWorker"
  args:
    - ""
    - award_first_post
    - ""
resave_supported_tags:
  description: "Resaves supported tags to recalculate scores (runs daily at 00:25 UTC)"
  cron: "25 0 * * *"
  class: "Tags::ResaveSupportedTagsWorker"
send_welcome_notifications:
  description: "Sends welcome notifications to new users (runs daily at 16:30 UTC)"
  cron: "0 16 * * *"
  class: "Broadcasts::SendWelcomeNotificationsWorker"
hourly_feed_cache_bust:
  description: "Busts the edge cache of the feed (runs hourly on the hour)"
  cron: "0 * * * *"
  class: "BustCachePathWorker"
  args:
    - "/feed.xml"
daily_home_cache_bust:
  description: "Busts the edge cache of the homepage (runs daily at 00:00 UTC)"
  cron: "0 0 * * *"
  class: "BustCachePathWorker"
  args:
    - "/"
send_email_digest:
  description: "Sends the email digest to users (runs at 11:30 UTC on Wednesday, Thursday, Friday, and Saturday)"
  cron: "30 11 * * 1,2,3,4,5"
  class: "Emails::EnqueueDigestWorker"
remove_old_notifications:
  description: "Deletes old notifications from the database (runs daily at 05:00 UTC)"
  cron: "0 5 * * *"
  class: "Notifications::RemoveOldNotificationsWorker"
remove_old_emails:
  description: "Deletes old emails we don't need to retain from the database (runs hourly at the 18th minute after the hour)"
  cron: "18 * * * *"
  class: "Emails::RemoveOldEmailsWorker"
sync_credits_counter_cache:
  description: "Synchronizes counter caches for credits (runs daily at 16:00 UTC)"
  cron: "0 16 * * *"
  class: "Credits::SyncCounterCache"
get_podcast_episodes:
  description: "Fetches podcast episodes asynchronously (runs hourly on the 5th minute after the hour)"
  cron: "5 * * * *"
  class: "Podcasts::EnqueueGetEpisodesWorker"
update_latest_github_repos:
  description: "Fetches the latest info about stored GitHub repositories (runs hourly, 45 min after the hour)"
  cron: "45 * * * *"
  class: "GithubRepos::UpdateLatestWorker"
push_notifications_cleanup:
  description: "Cleans up Push Notifications from Redis (runs every 8 hours on the 20th minute after the hour)"
  cron: "20 */8 * * *"
  class: "PushNotifications::CleanupWorker"
capture_query_stats:
  description: "Collects Postgres query stats for PGHero (runs every 5 minutes)"
  cron: "*/5 * * * *"
  class: "CaptureQueryStatsWorker"
notify_about_published_articles:
  description: "Sends notifications about the new published articles (runs every minute)"
  cron: "*/5 * * * *"
  class: "Articles::PublishWorker"
billboard_event_rollup:
  description: "Compact rows in the display_ad_events table"
  cron: "5 3 * * *"
  class: "BillboardEventRollupWorker"
audience_segment_refresh:
  description: "Refresh audience segmentation for all segments"
  cron: "10 3 * * *"
  class: "AudienceSegmentRefreshAllWorker"
drip_email_worker:
  description: "Send drip emails to users based on their registration date"
  cron: "37 * * * *" # Every hour on the 37 minute
  class: "Emails::DripEmailWorker"
cleanup_orphaned_images:
  description: "Clean up orphaned uploaded images (runs daily at 2:30 AM)"
  cron: "30 2 * * *"
  class: "Images::CleanupOrphanedWorker"

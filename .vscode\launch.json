{
  // Use IntelliSense to learn about possible attributes.
  // Hover to view descriptions of existing attributes.
  // For more information, visit: https://go.microsoft.com/fwlink/?linkid=830387
  "version": "0.2.0",
  "configurations": [
    {
      "name": "Attach to Node",
      "port": 9229,
      "request": "attach",
      "skipFiles": ["node_modules/*", "<node_internals>/**/*.js"],
      "type": "node"
    },
    {
      "name": "Attach to Edge",
      "type": "vscode-edge-devtools.debug",
      "request": "attach",
      "port": 2015,
      "webRoot": "${workspaceFolder}"
    },
    {
      "name": "Attach to Chrome",
      "type": "chrome",
      "request": "attach",
      "port": 9222,
      "webRoot": "${workspaceFolder}/app/javascript/packs"
    },
    {
      "name": "Attach to Existing Server",
      "type": "ruby_lsp",
      "request": "attach"
    },
    {
      "name": "RSpec - all",
      "type": "ruby_lsp",
      "request": "launch",
      "program": "${workspaceRoot}/bin/rspec",
    },
    {
      "name": "RSpec - active spec file only",
      "type": "ruby_lsp",
      "request": "launch",
      "program": "${workspaceRoot}/bin/rspec ${file}",
    },
    {
      "name": "Jest Current File",
      "type": "node",
      "request": "launch",
      "program": "${workspaceFolder}/node_modules/.bin/jest",
      "args": ["${relativeFile}"],
      "console": "integratedTerminal",
      "internalConsoleOptions": "neverOpen",
      "windows": {
        "program": "${workspaceFolder}/node_modules/jest/bin/jest"
      },
      "skipFiles": ["node_modules/*", "<node_internals>/**/*.js"]
    }
  ]
}

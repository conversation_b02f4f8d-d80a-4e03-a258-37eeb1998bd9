require "rails_helper"

RSpec.describe Images::CleanupO<PERSON>hane<PERSON><PERSON><PERSON><PERSON>, type: :worker do
  let(:user) { create(:user) }
  let(:worker) { described_class.new }
  
  describe "#perform" do
    context "with orphaned images" do
      let!(:orphaned_image) do
        create(:image_upload, 
               user: user, 
               used_in_content: false, 
               created_at: 8.days.ago)
      end
      
      let!(:used_image) do
        create(:image_upload, 
               user: user, 
               used_in_content: true, 
               created_at: 8.days.ago)
      end
      
      let!(:recent_orphaned_image) do
        create(:image_upload, 
               user: user, 
               used_in_content: false, 
               created_at: 1.day.ago)
      end
      
      before do
        # Create actual files for testing
        FileUtils.mkdir_p(File.dirname(orphaned_image.full_file_path))
        FileUtils.mkdir_p(File.dirname(used_image.full_file_path))
        FileUtils.mkdir_p(File.dirname(recent_orphaned_image.full_file_path))
        
        File.write(orphaned_image.full_file_path, "test content")
        File.write(used_image.full_file_path, "test content")
        File.write(recent_orphaned_image.full_file_path, "test content")
      end
      
      after do
        # Clean up test files
        [orphaned_image, used_image, recent_orphaned_image].each do |img|
          File.delete(img.full_file_path) if File.exist?(img.full_file_path)
        end
      end
      
      it "removes only old orphaned images" do
        expect { worker.perform(7) }.to change { ImageUpload.count }.by(-1)
        
        expect { orphaned_image.reload }.to raise_error(ActiveRecord::RecordNotFound)
        expect(used_image.reload).to be_present
        expect(recent_orphaned_image.reload).to be_present
      end
      
      it "deletes the physical files" do
        worker.perform(7)
        
        expect(File.exist?(orphaned_image.full_file_path)).to be false
        expect(File.exist?(used_image.full_file_path)).to be true
        expect(File.exist?(recent_orphaned_image.full_file_path)).to be true
      end
      
      it "returns the count of cleaned up images" do
        result = worker.perform(7)
        expect(result).to eq(1)
      end
      
      it "logs cleanup results" do
        expect(Rails.logger).to receive(:info).with(/Starting orphaned image cleanup/)
        expect(Rails.logger).to receive(:info).with(/cleanup completed: 1 files removed/)
        
        worker.perform(7)
      end
      
      it "sends metrics to monitoring" do
        expect(ForemStatsClient).to receive(:count).with("images.cleanup.orphaned_removed", 1)
        expect(ForemStatsClient).to receive(:gauge).with("images.cleanup.storage_freed_bytes", anything)
        
        worker.perform(7)
      end
    end
    
    context "with no orphaned images" do
      it "completes without errors" do
        expect { worker.perform(7) }.not_to raise_error
      end
      
      it "returns zero count" do
        result = worker.perform(7)
        expect(result).to eq(0)
      end
    end
    
    context "when file deletion fails" do
      let!(:orphaned_image) do
        create(:image_upload, 
               user: user, 
               used_in_content: false, 
               created_at: 8.days.ago)
      end
      
      before do
        allow(File).to receive(:exist?).and_return(true)
        allow(File).to receive(:delete).and_raise(StandardError, "Permission denied")
      end
      
      it "handles errors gracefully" do
        expect(Rails.logger).to receive(:error).with(/Failed to remove orphaned image/)
        expect(Honeybadger).to receive(:notify)
        
        expect { worker.perform(7) }.not_to raise_error
      end
    end
  end
end

class CreateImageUploads < ActiveRecord::Migration[7.0]
  def change
    create_table :image_uploads do |t|
      t.references :user, null: false, foreign_key: true
      t.string :filename, null: false
      t.string :file_path, null: false
      t.integer :file_size, null: false
      t.string :content_type, null: false
      t.boolean :used_in_content, default: false, null: false
      t.datetime :used_at
      t.string :upload_source, default: 'web' # web, api, etc.
      
      t.timestamps
    end
    
    add_index :image_uploads, [:user_id, :created_at]
    add_index :image_uploads, [:used_in_content, :created_at]
    add_index :image_uploads, :filename
    add_index :image_uploads, :file_path
  end
end

---
name: "[Core Team Only]: New Approved Feature"
about: This template is for Core Team only. For feature requests, please use GitHub
  Discussions.
title: ''
labels: ''
assignees: ''

---

<!-- ⚠️⚠️⚠️ This issue template is for Core Team only to outline approved features.  For new Feature Requests, please use GitHub Discussions: https://github.com/forem/forem/discussions -->

## Is this feature related to a problem? Please describe.

<!-- Be sure to cover the Who / What / Why.  IE: As a (role), I want (function) so that (value).-->

## Describe the solution you’d like

<!-- Describe the end state that solves your problem. -->

## Definition of Done

<!-- The granular tasks / acceptance criteria that need to be completed as part of this issue. -->

- [ ] Task 1
- [ ] Task 2
- [ ] Task 3

## Additional Context

<!-- Please share any implementation notes, specific requirements, potential rabbit holes, historical knowledge, etc. -->

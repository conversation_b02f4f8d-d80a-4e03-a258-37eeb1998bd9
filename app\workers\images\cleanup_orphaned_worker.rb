module Images
  class CleanupOrphanedWorker
    include Sidekiq::Job

    sidekiq_options queue: :low_priority, retry: 10, lock: :until_and_while_executing

    # Default retention period for orphaned images (7 days)
    DEFAULT_RETENTION_DAYS = 7

    def perform(retention_days = DEFAULT_RETENTION_DAYS)
      retention_period = retention_days.days.ago
      
      Rails.logger.info("Starting orphaned image cleanup for images older than #{retention_period}")
      
      cleanup_count = 0
      storage_freed = 0
      
      # Find all uploaded images that are not referenced in articles or comments
      orphaned_images = find_orphaned_images(retention_period)
      
      orphaned_images.find_each do |image_record|
        begin
          file_size = get_file_size(image_record)
          
          # Remove the physical file
          remove_image_file(image_record)
          
          # Track metrics
          cleanup_count += 1
          storage_freed += file_size
          
          Rails.logger.debug("Removed orphaned image: #{image_record.inspect}")
          
        rescue StandardError => e
          Rails.logger.error("Failed to remove orphaned image #{image_record.inspect}: #{e.message}")
          Honeybadger.notify(e, context: { image_record: image_record.inspect })
        end
      end
      
      # Log cleanup results
      Rails.logger.info("Orphaned image cleanup completed: #{cleanup_count} files removed, #{format_bytes(storage_freed)} freed")
      
      # Send metrics to monitoring
      ForemStatsClient.count("images.cleanup.orphaned_removed", cleanup_count)
      ForemStatsClient.gauge("images.cleanup.storage_freed_bytes", storage_freed)
      
      cleanup_count
    end

    private

    def find_orphaned_images(retention_period)
      # This is a simplified approach - in reality, we'd need to track image usage
      # For now, we'll identify images by scanning upload directories and checking usage
      
      # Get all image files from upload directory that are older than retention period
      upload_pattern = Rails.root.join("public/uploads/**/*.{jpg,jpeg,png,gif,webp,bmp,ico,dng}")
      old_files = Dir.glob(upload_pattern).select do |file_path|
        File.mtime(file_path) < retention_period
      end
      
      # Check which files are not referenced in articles or comments
      orphaned_files = old_files.reject { |file_path| image_referenced?(file_path) }
      
      # Return file info for cleanup
      orphaned_files.map { |path| { path: path, size: File.size(path) } }
    end

    def image_referenced?(file_path)
      filename = File.basename(file_path)
      relative_path = file_path.gsub(Rails.root.join("public").to_s, "")
      
      # Check if image is referenced in any article body_markdown or processed_html
      article_referenced = Article.where(
        "body_markdown LIKE ? OR processed_html LIKE ?", 
        "%#{filename}%", 
        "%#{relative_path}%"
      ).exists?
      
      return true if article_referenced
      
      # Check if image is referenced in any comment body_markdown or processed_html
      comment_referenced = Comment.where(
        "body_markdown LIKE ? OR processed_html LIKE ?", 
        "%#{filename}%", 
        "%#{relative_path}%"
      ).exists?
      
      return true if comment_referenced
      
      # Check if image is used as main_image or social_image
      main_image_referenced = Article.where(
        "main_image LIKE ? OR social_image LIKE ?",
        "%#{filename}%",
        "%#{filename}%"
      ).exists?
      
      return true if main_image_referenced
      
      # Check if image is used as profile image
      profile_image_referenced = User.where("profile_image LIKE ?", "%#{filename}%").exists?
      
      profile_image_referenced
    end

    def remove_image_file(image_record)
      file_path = image_record[:path]
      
      if File.exist?(file_path)
        File.delete(file_path)
        Rails.logger.debug("Deleted file: #{file_path}")
      else
        Rails.logger.warn("File not found for deletion: #{file_path}")
      end
    end

    def get_file_size(image_record)
      image_record[:size] || 0
    end

    def format_bytes(bytes)
      units = %w[B KB MB GB TB]
      size = bytes.to_f
      unit_index = 0
      
      while size >= 1024 && unit_index < units.length - 1
        size /= 1024
        unit_index += 1
      end
      
      "#{size.round(2)} #{units[unit_index]}"
    end
  end
end

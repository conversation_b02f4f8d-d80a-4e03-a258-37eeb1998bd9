function buildCommentFormHTML(commentableId, commentableType, parentId) {
  var authToken = document.querySelector("meta[name='csrf-token']").getAttribute('content');
  var user = userData();
  var codeOfConductHTML = ""
  if (user && !user.codeOfConduct && user.commentCount < 1){
    codeOfConductHTML =   '<div class="code-of-conduct sub-comment-code-of-conduct" style="display:block" id="toggle-code-of-conduct-checkbox">\
                            <input class="checkbox" type="checkbox" name="checked_code_of_conduct" required />\
                            <label for="checked_code_of_conduct">I\'ve read the <a href="/code-of-conduct">code of conduct</a></label>\
                          </div>'
  }
  var randomIdNumber = Math.floor(Math.random() * 1991);

  return `<form class="comment-form pt-4" onsubmit="handleCommentSubmit.bind(this)(event)" id="new-comment-${parentId}" action="/comments" accept-charset="UTF-8" method="post" data-comment-id="${parentId}">
      <input name="utf8" type="hidden" value="&#x2713;" />
      <input type="hidden" name="authenticity_token" value="${authToken}">
      <input value="${commentableId}" type="hidden" name="comment[commentable_id]" id="comment_commentable_id" />
      <input value="${commentableType}" type="hidden" name="comment[commentable_type]" id="comment_commentable_type" />
      <input value="${parentId}" type="hidden" name="comment[parent_id]" id="comment_parent_id" />
      <div class="comment-form__inner">
        <div class="comment-form__field">
          <textarea id="textarea-for-${parentId}" class="crayons-textfield crayons-textfield--ghost comment-textarea" name="comment[body_markdown]" data-tracking-name="comment_form_textfield" placeholder="Reply..." aria-label="Reply to a comment..." required="required" onkeydown="handleKeyDown(event)" onfocus="handleFocus(event)" oninput="handleChange(event)" onkeyup="handleKeyUp(event)"></textarea>
        </div>
        <div class="response-templates-container crayons-card crayons-card--secondary p-4 mb-4 fs-base comment-form__templates hidden">
          <header>
            <button type="button" class="personal-template-button active" data-target-type="personal" data-form-id="new_comment">Personal</button>
            <button type="button" class="moderator-template-button hidden" data-target-type="moderator" data-form-id="new_comment">Trusted User</button>
          </header>
          <div class="personal-responses-container"></div>
          <div class="moderator-responses-container hidden"></div>
          <a target="_blank" rel="noopener nofollow" href="/settings/response-templates">Create template</a>
          <p>Templates let you quickly answer FAQs or store snippets for re-use.</p>
        </div>
        <div class="comment-form__preview text-styles text-styles--secondary"></div>
        <div class="comment-form__buttons mb-4 whitespace-nowrap">
          <button type="submit" class="crayons-btn comment-action-button mr-2 js-btn-enable" name="submit" data-tracking-name="comment_reply_submit_button" disabled>Submit</button>
          <button type="button" class="preview-toggle crayons-btn crayons-btn--secondary comment-action-button comment-action-preview mr-2 js-btn-enable" data-tracking-name="comment_reply_preview_button" onclick="handleCommentPreview(event)" disabled>Preview</button>
          <button type="button" class="crayons-btn crayons-btn--ghost" data-tracking-name="comment_reply_dismiss_button" onclick="handleFormClose(event)">Dismiss</button>
        </div>
      </div>
    </form>`;
}

---
name: Build ghcr.io/forem/ruby Container Image
on:
  workflow_call:
  # Allow manual runs through GitHub GUI in case of emergency.
  workflow_dispatch:
  push:
    branches:
      - 'main'
    paths:
      - 'Containerfile.base'
      - '.ruby-version-next'
  pull_request:
    branches:
      - 'main'
    paths:
      - 'Containerfile.base'
      - '.ruby-version-next'

jobs:
  build-image:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout
        uses: actions/checkout@v3
        with:
          fetch-depth: "2" # Get current and preceding commit only
      - name: Detect relevant changed files in this job
        id: containerfile-changed
        uses: tj-actions/changed-files@v37
        with:
          files: Containerfile.base
      - name: Do not push to GHCR if this commit does not target the main branch
        if: ${{ github.event_name != 'push' && github.event_name != 'workflow_dispatch' }}
        run: echo "SKIP_PUSH=1" >> $GITHUB_ENV
      - name: Set up QEMU for cross-compiling to ARM64
        uses: docker/setup-qemu-action@v2
      - name: Set up Docker BuildX
        id: buildx
        uses: docker/setup-buildx-action@v2
      - name: Login to GitHub Container Registry
        if: ${{ github.event_name == 'push' || github.event_name == 'workflow_dispatch' }}
        uses: docker/login-action@v2
        with:
          registry: ghcr.io
          username: ${{ github.repository_owner }}
          password: ${{ github.token }}
      - name: Build Images
        env:
          EXTERNAL_QEMU: "1"
        run: scripts/build_base_ruby_image.sh

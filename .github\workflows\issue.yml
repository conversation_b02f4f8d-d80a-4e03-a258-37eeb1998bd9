# This workflow posts an automated comment on every new issue
# https://github.com/marketplace/actions/create-or-update-comment (https://github.com/peter-evans/create-or-update-comment)

name: Automatic Comment
on:
  issues:
    types: [opened]
permissions:
  contents: read

jobs:
  comment:
    permissions:
      issues: write  # for peter-evans/create-or-update-comment to create or update comment
      pull-requests: write  # for peter-evans/create-or-update-comment to create or update comment
    name: Comment
    runs-on: ubuntu-latest
    steps:
      - name: Automatic Comment
        uses: peter-evans/create-or-update-comment@v2
        with:
          issue-number: ${{ github.event.issue.number }}
          body: |
            Thanks for the issue, we will take it into consideration! Our team of engineers is busy working on many types of features, please give us time to get back to you.
            
            To our amazing contributors: [issues labeled `bug`](https://github.com/forem/forem/issues?q=is%3Aissue+is%3Aopen+label%3Abug) are always up for grabs, but for feature requests, please wait until we add a `ready for dev` before starting to work on it.

            If this is a feature request from an external contributor (not core team at Forem), please close the issue and re-post via [GitHub Discussions](https://github.com/forem/forem/discussions/categories/feature-requests).

            To claim an issue to work on, please leave a comment. If you've claimed the issue and need help, please ping @forem-team. The OSS Community Manager or the engineers on OSS rotation will follow up.

            For full info on how to contribute, please check out our [contributors guide](https://developers.forem.com/contributing-guide/forem).

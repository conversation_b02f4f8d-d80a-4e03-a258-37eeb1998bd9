class CreateImageUsages < ActiveRecord::Migration[7.0]
  def change
    create_table :image_usages do |t|
      t.references :image_upload, null: false, foreign_key: true
      t.references :usable, polymorphic: true, null: false
      t.integer :usage_type, default: 0, null: false
      
      t.timestamps
    end
    
    add_index :image_usages, [:image_upload_id, :usable_type, :usable_id, :usage_type], 
              unique: true, 
              name: 'index_image_usages_unique'
    add_index :image_usages, [:usable_type, :usable_id]
    add_index :image_usages, :usage_type
  end
end

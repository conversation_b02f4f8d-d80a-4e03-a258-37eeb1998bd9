name: Buildkite
on:
  issue_comment:
    types: [created]
jobs:
  add_ci_label:
    name: "Add CI label to pull request"
    if: github.event.issue.pull_request != '' && contains(github.event.comment.body, '/ci') && (github.event.comment.author_association == 'MEMBER' || github.event.comment.author_association == 'OWNER' || github.event.comment.author_association == 'CONTRIBUTOR')
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - uses: actions-ecosystem/action-add-labels@v1
        with:
          github_token: ${{ secrets.github_token }}
          labels: ci

  build_containers:
    name: "Run forem/build-containers pipeline"
    if: github.event.issue.pull_request != '' && contains(github.event.comment.body, '/ci') && (github.event.comment.author_association == 'MEMBER' || github.event.comment.author_association == 'OWNER' || github.event.comment.author_association == 'CONTRIBUTOR')
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - run: ./scripts/create_buildkite_pr_build.sh
        env:
          BUILDKITE_API_ACCESS_TOKEN: ${{ secrets.buildkite_api_access_token }}
          PIPELINE: "forem/build-containers"
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
          PULL_REQUEST_ID: ${{ github.event.issue.number }}
